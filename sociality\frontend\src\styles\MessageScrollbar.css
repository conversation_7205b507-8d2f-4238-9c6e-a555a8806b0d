/**
 * AGGRESSIVE SCROLLBAR ELIMINATION
 * Completely removes all scrollbar tracks and spaces
 */

/* NUCLEAR OPTION - Hide ALL scrollbars everywhere */
* {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  scrollbar-gutter: none !important;
}

*::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

*::-webkit-scrollbar-track {
  display: none !important;
  background: transparent !important;
}

*::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

*::-webkit-scrollbar-corner {
  display: none !important;
  background: transparent !important;
}

/* React Window List - FORCE NO SCROLLBARS */
.react-window-list {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  flex: 1 1 0 !important;
  min-height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
  overflow: auto !important;
}

.react-window-list::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.react-window-list::-webkit-scrollbar-track {
  display: none !important;
}

.react-window-list::-webkit-scrollbar-thumb {
  display: none !important;
}

.react-window-list::-webkit-scrollbar-corner {
  display: none !important;
}

/* ELIMINATE ALL CONTAINER SPACING AND SCROLLBARS */
.message-container,
.message-scrollbar-container {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  flex: 1 1 0 !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  padding: 0 !important;
}

.message-container::-webkit-scrollbar,
.message-scrollbar-container::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
}
